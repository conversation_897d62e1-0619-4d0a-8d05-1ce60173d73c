# 分层平面图生成命令 (Floor Plan Generation Command)

## 命令名称
`RES_GENERATE_FLOOR_PLANS`

## 功能描述
该命令用于从包含XData属性的CAD实体中自动生成分层平面图。命令会扫描当前图纸中的所有实体，提取XData中的建筑、单元、楼层信息，然后为每个建筑-楼层组合生成独立的平面图文件。

## XData数据结构要求
命令支持两种XData应用程序名称的实体：

### CDCH HI 数据结构
应用程序名称为 "CDCH HI" 的XData，数据结构如下：
- 索引 2: 建筑号 (栋) - 例如 "2"
- 索引 3: 楼层号 (楼层) - 例如 "1"
- 索引 4: 单元号 (单元) - 例如 "2"
- 索引 5: 房间号 (房号) - 例如 "201"
- 索引 23: 指北针方向 (度数) - 例如 "90"

### CDCHPPI 数据结构
应用程序名称为 "CDCHPPI" 的XData，数据结构如下：
- 索引 3: 建筑号 (栋) - 例如 "25"
- 索引 4: 楼层号 (楼层) - 例如 "4"
- 注：CDCHPPI实体通常不包含单元号、房号和指北针方向信息

### 北向箭头继承机制
当同一建筑-楼层组合中包含多种XData类型的实体时：
- CDCHPPI实体会自动继承同组中CDCH HI实体的指北针方向信息
- 如果同组中有多个CDCH HI实体，使用找到的第一个有效指北针值
- 如果同组中没有CDCH HI实体，CDCHPPI实体使用默认值0
- 每个实体只包含一种XData应用程序名称（CDCH HI 或 CDCHPPI）

## 使用步骤

1. **启动命令**
   在AutoCAD命令行中输入：`RES_GENERATE_FLOOR_PLANS`

2. **自动处理**
   命令会自动：
   - 创建输出文件夹（无需用户选择）
   - 扫描所有实体的XData（支持CDCH HI和CDCHPPI）
   - 按建筑-楼层分组（混合不同XData类型）
   - 处理北向箭头继承逻辑
   - 为每个组合生成平面图
   - 添加单元标签和楼层标签
   - 添加外框和指北针
   - 保存为独立的DWG文件

## 自动输出位置
命令会自动选择输出位置：
- **已保存图纸**：在图纸文件所在目录下创建"分层平面图输出"文件夹
- **未保存图纸**：在桌面创建"分层平面图输出"文件夹
- **时间戳子文件夹**：每次运行都会创建带时间戳的子文件夹，避免文件冲突
- **示例路径**：`C:\Users\<USER>\Desktop\分层平面图输出\20241216_143022\`

## 输出文件
- 文件命名格式：`[建筑号]栋[楼层号]层平面图.dwg`
- 例如：`2栋1层平面图.dwg`

## 生成内容
每个生成的平面图包含：

1. **原始实体**：复制自原图纸的所有相关实体
2. **单元标签**：在每个单元底部显示"X单元"
3. **楼层标签**：在整个楼层底部显示"X栋Y层平面图"
4. **外框**：红色边框包围整个平面图
5. **指北针**：根据XData中的方向值定向的指北针
6. **图框信息**：包含比例和图号信息

## 注意事项

1. **数据要求**：实体必须包含有效的XData（"CDCH HI" 或 "CDCHPPI"），至少需要建筑号和楼层号
2. **自动保存**：命令会自动选择输出位置，无需用户干预
3. **时间戳文件夹**：每次运行都会创建新的时间戳文件夹，避免文件冲突
4. **文件夹权限**：确保对图纸目录或桌面有写入权限
5. **内存使用**：处理大量实体时可能需要较多内存

## 错误处理
- 如果没有找到有效的XData，命令会提示并退出
- 如果输出文件夹无法创建或访问，会显示错误信息
- 生成过程中的任何错误都会在命令行中显示

## 技术实现
- 使用AutoCAD .NET API
- 支持事务管理确保数据一致性
- 实现了实体克隆和数据库操作
- **优化的块参照处理**：使用Wblock方法替代WblockCloneObjects，确保块参照及其定义正确复制
- **完整的设置复制**：自动复制文字样式、图层设置等，确保中文字体正确显示
- **块参照属性处理**：自动处理块参照的属性可见性和默认值
- **智能模板系统**：
  - 自动读取 `Template\Printeddiagram.dwg` 模板文件
  - 识别红色多线段作为边界定位基准
  - 智能缩放和定位模板到分层图内容
  - 自动处理指北针旋转（黄色实体）
  - 保持模板完整性，避免重复图框信息
- 包含完整的错误处理和日志记录

## 模板文件要求
模板文件位置：`Template\Printeddiagram.dwg`

**模板文件规范：**
- **红色多线段（颜色索引=1）**：必须是闭合的多线段，用作分层图内容的边界定位基准
- **黄色实体（颜色索引=2）**：指北针相关实体，会根据分层图的指北针方向自动旋转
- **其他实体**：图框、标题栏、比例信息等，会按比例缩放并保持相对位置

**模板处理逻辑：**
1. 系统读取模板文件并识别红色边界多线段
2. 计算分层图内容边界，添加适当边距
3. 计算缩放比例，使红色边界适配分层图内容
4. 复制所有模板实体并应用变换
5. 根据分层图指北针方向旋转黄色指北针实体

## 使用方法
1. 确保模板文件 `Template\Printeddiagram.dwg` 存在且符合规范
2. 在AutoCAD中输入命令：`RES_GENERATE_FLOOR_PLANS`
3. 等待自动处理完成（无需任何用户交互）
4. 查看命令行显示的输出路径

## 扩展功能
未来可以扩展的功能：
- 支持模板文件自定义
- 批量处理多个图纸
- 更复杂的图框样式
- 自定义标签格式
- 导出为其他格式（PDF、图片等）
